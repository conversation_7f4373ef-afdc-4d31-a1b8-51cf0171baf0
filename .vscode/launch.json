{"version": "0.2.0", "configurations": [{"name": "Debug path_tool", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/plus", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set disassembly flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "miDebuggerPath": "/usr/bin/gdb"}]}