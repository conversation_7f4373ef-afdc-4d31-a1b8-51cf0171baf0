// 要求所有子串不能与0 ，那么就不能存在等于0 的元素，


// def longest_non_zero_substring(s):
//     """
//     找到字符串中最长的非零子串。

//     Args:
//         s: 输入字符串。

//     Returns:
//         最长非零子串的长度。
//     """
//     max_len = 0
//     current_len = 0
//     for char in s:
//         if char != '0':
//             current_len += 1
//             max_len = max(max_len, current_len)
//         else:
//             current_len = 0
//     return max_len