


#include <iostream>
#include <vector>
#include <cmath>
#include <string>
using namespace std;

// 质数检测函数
bool isPrime(int num) {
    if (num <= 1) return false;
    if (num == 2) return true;
    if (num % 2 == 0) return false;
    for (int i = 3; i * i <= num; i += 2) {
        if (num % i == 0) return false;
    }
    return true;
}

// 通过率60%。
// 生成所有可能的表达式并计算总和
void generateExpressions(const string& s, vector<int>& results, int pos, int current, int sum, std::vector<int> path) {
    if (pos == s.length()) {
        sum += current;
        for (auto ch : path) {
            std::cout << ch << ", ";
        }
        std::cout << std::endl;
        std::cout << "current:" << current << std::endl;
        results.push_back(sum);
        return;
    }
    
    // 不插入加号，继续构建当前数字
    generateExpressions(s, results, pos + 1 , current * 10 + (s[pos] - '0'), sum, path);
    
    // 插入加号，将当前数字加到sum中，并重置current
    if (pos != 0) {  // 不在开头插入加号
        path.push_back(current);
        generateExpressions(s, results, pos + 1, s[pos] - '0', sum + current, path);
        path.push_back(current);
    }
}

int main() {
    string input;
    cin >> input;

    std::cout << "test" << std::endl;
    
    vector<int> results;
    std::vector<int> path;
    generateExpressions(input, results, 0, 0, 0, path);
    
    long long sum = 0;
    for (int res : results) {
        sum += res;
    }
    cout <<sum << endl;
    
    return 0;
}
