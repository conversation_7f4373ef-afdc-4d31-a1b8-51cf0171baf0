// #include <iostream>

// int main() {
//     std::cout << "Hello, World!" << std::endl;
//     return 0;
// }

#include <iostream>
#include <vector>
#include <algorithm>
#include <numeric>
#include <unordered_map>
#include <unordered_set>
#include <set>
#include <stack>
#include <functional>
#include <math.h>
#include <queue>
#include <deque>
#include <map>
#include <thread>
#include <string.h>
#include <limits.h>
using namespace std;
using ll = long long;
using pii = pair<int,int>;
const int MOD = 1e9+7;
  

//
// 小美的数组删除
// 删除第一个元素，同时数组的长度减去1， cost = x,
// 删除整个数组， cost = k * mex(vec)
// 返回删除数组的最小cost
int main()
{
    int t;
    cin>>t;
    while(t--){
        int n;
        cin>>n;
        ll k,x;
        cin>>k>>x;
  
        vector<int> a(n);
        vector<int> cnt(n+1);
        for(int i=0;i<n;i++){
            cin>>a[i];
            cnt[a[i]]++; // 统计出现次数
        }
        int mex;
        for(int i=0;i<=n;i++){
            if(cnt[i]==0){
                mex = i; // 第一个没有出现的数就是mex。
                break;
            }
        }
        ll ans = k*mex;
        ll cur = 0;
        for(int i=0;i<n;i++){
            cur += x;
            cnt[a[i]]--; // 删除这个元素
            if(cnt[a[i]]==0){
                mex = min(mex,a[i]);
            }
            ans = min(ans,cur + k*mex);
        }
        cout<<ans<<endl;
    }
}
