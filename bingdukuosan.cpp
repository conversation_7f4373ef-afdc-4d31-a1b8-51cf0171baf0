#include <iostream>
#include <string>
#include <vector>
#include <bits/stdc++.h>
using namespace std;



// int main() {
//     int num;
//     cin >> num;

//     while(num--) {
//         int x, y, t;
//         cin >>x >>y >> t;
//         std::vector<std::vector<int>> dp(x+1, std::vector(y+1, 0));
//         std::vector<std::vector<int>> new_dp = dp;
//         dp[0][0] = 1;

//         if (t == 0) {
//             std::cout << dp[x][y] << std::endl;
//         }

//         for (int time = 1; time <= t; time++) {
//             for (int i = 0; i <= x; i++) {
//                 for (int j = 0; j <=y; j++) {
//                     if (dp[i][j] > 0) {
//                         if (i+1<=x) {
//                             new_dp[i + 1][j] = dp[i][j] + 1;
//                         }
//                        if (j+1 <=y) {
//                            new_dp[i][j+1] = dp[i][j] + 1;
//                        }
//                     }
//                 }
//             }
//             dp = new_dp;
//             new_dp = std::vector<std::vector<int>>(x+1, std::vector(y+1, 0));
//         }

//         std::cout << dp[x][y] << std::endl;

//     }

//     return 0;
// }

#include <bits/stdc++.h>
using namespace std;
typedef long long ll;
const int N = 5005;
const ll mod = 998244353LL;
ll c[N][N];
void init()
{
    for (int i = 0; i < N; i++)
        c[i][0] = 1LL;
    for (int i = 1; i < N; i++)
        for (int j = 1; j < N; j++)
            c[i][j] = (c[i - 1][j] + c[i - 1][j - 1]) % mod;
}
int main()
{
    int n, x, y, t;
    cin >> n;
    init();
    while (n--)
    {
        cin >> x >> y >> t;
        if (t < x + y)
            cout << 0 << endl;
        else
            cout << (c[t][x] % mod * c[t - x][y] % mod) << endl;
    }
    return 0;
}


#include<bits/stdc++.h>
using namespace std;
const int mod=998244353;
long long a[5005][5005];
int main111(){
	
    for (int i = 0; i < 2005; i++) {
        a[i][0] = 1;
    }
	// a[0][0]=1;
	for (int i=1;i<=5000;i++){//建表
		for (int j=i;j>=0;j--){
			a[i][j]=(a[i-1][j-1]+a[i-1][j])%mod;
		}
	}
	int n,x,y,t;
	scanf("%d",&n);
	while(n--){
		scanf("%d %d %d",&x,&y,&t);
		if ((x+y)>t){//当给出的点在目前已经建成的三角形外时
			printf("0\n");
			continue;
		}
		if (x==0){//处于边界
			printf("%d\n",a[t][y]);
		}
		else if (y==0){//处于边界
			printf("%d\n",a[t][x]);
		}
		else if (x+y==t){//处于边界
			printf("%d\n",a[t][x]);
		}
		else {//位于三角形内部
			printf("%d\n",(a[x+y][x]*a[t][x+y])%mod);//
		}
	}
}
// https://blog.csdn.net/zzzck/article/details/105747931
