#include <iostream>
#include <vector>
using namespace std;

void BubbleSort(vector<int>& a) {
	for (int i = 0; i < a.size() - 1; ++i) {
		for (int j = 0; j < a.size() - 1 - i; ++j) {
			if (a[j] < a[j + 1]) {
				int tmp = a[j];
				a[j] = a[j + 1];
				a[j + 1] = tmp;
			}
		}
	}
	return;
}

int LargestSquare(vector<int>& a) {
	BubbleSort(a);
	int nMaxLength = 1;
	for (int cur = 1; cur < a.size(); ++cur) {
		if (nMaxLength >= a[cur]) {
			break;
		}
		else {
			++nMaxLength;
		}
	}
	return nMaxLength;
}

int main() {
	int n;
	cin >> n;
	vector<int> a(n);
	for (int i = 0; i < n; ++i) {
		int ai;
		cin >> ai;
		a[i] = ai;
	}
	int result = LargestSquare(a);
	cout << result << endl;
	return 0;
}
