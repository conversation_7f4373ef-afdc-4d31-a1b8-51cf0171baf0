#include <iostream>
using namespace std;
// 易得只要0和1个数为奇数时不可通过操作获得
// 小红拿到了一个01串，她每次可以选择一个长度为2的连续子串取反（0变1，1变0），她想知道，是否能在有限的操作次数内使得所有字符相同？ 
// 同时翻转2位
int main() {
    int q;
    cin >> q;
    string str;
    while (q--) {
        int cnt1 = 0, cnt0 = 0;
        cin >> str;
        for (int i = 0, len = str.size(); i < len; i++) {
            if ((str[i] - '0') & 1)cnt1++;
            else cnt0++;
        }
        if (cnt1 & 1 && cnt0 & 1)cout << "No" << endl;
        else cout << "Yes" << endl;
    }
}
// 64 位输出请用 printf("%lld")