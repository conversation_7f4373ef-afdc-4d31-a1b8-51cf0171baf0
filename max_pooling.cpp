
#include <vector>
#include <algorithm>
#include <limits>

class MatrixPooling {
public:
    static int secondPooling(const std::vector<std::vector<int>>& matrix) {
        return pool(matrix).front().front();
    }
private:
    static std::vector<std::vector<int>> pool(const std::vector<std::vector<int>>& matrix) {
        int n = matrix.size();
        if (n == 1) return matrix;
        int newSize = n / 2;
        std::vector<std::vector<int>> newMatrix(newSize, std::vector<int>(newSize));

        for (int i = 0; i < n; i += 2) {
            for (int j = 0; j < n; j += 2) {
                // 收集当前2x2子矩阵的元素
                std::vector<int> subMatrix;
                for (int x = 0; x < 2; ++x) {
                    for (int y = 0; y < 2; ++y) {
                        subMatrix.push_back(matrix[i + x][j + y]);
                    }
                }
                
                // 排序并取第二大的值
                std::sort(subMatrix.begin(), subMatrix.end());
                newMatrix[i / 2][j / 2] = subMatrix[subMatrix.size() - 2];
            }
        }
        
        return pool(newMatrix);
    }
};
