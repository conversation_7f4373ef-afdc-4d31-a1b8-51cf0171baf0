
#include <iostream>
#include <vector>
using namespace std;

int findLCA(int a, int b) {
    if (a > b) swap(a, b);
    vector<int> path1, path2;
    while (a > 1) {
        path1.push_back(a);
        a = (a & (a - 1)) + 1; // 向上找父节点, 去掉最后的一个0，然后再加1？
    }
    while (b > 1) {
        path2.push_back(b);
        b = (b & (b - 1)) + 1;
    }
    int i = 0;
    while (i < path1.size() && i < path2.size()) {
        if (path1[i] == path2[i]) {
            break;
        }
        i++;
    }
    return path1[i];
}

int main() {
    int a = 8, b = 12;
    cout << "LCA: " << findLCA(a, b) << endl;
    return 0;
}
