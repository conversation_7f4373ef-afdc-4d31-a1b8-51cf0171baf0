#include <iostream>
#include <vector>
#include <numeric>

using namespace std;

bool backtrack(const vector<int>& prefix, int start, int end, int k, vector<int>& partitions);

bool checkArrayPartition(vector<int>& nums, int m) {
    int n = nums.size();
    if (m <= 1 || m >= n) return false;
    
    // Precompute prefix sums
    vector<int> prefix(n+1, 0);
    for (int i = 0; i < n; i++) {
        prefix[i+1] = prefix[i] + nums[i];
    }
    
    // Try all possible k values (positions where the condition should hold)
    for (int k = 2; k < m; k++) {
        vector<int> partitions;
        if (backtrack(prefix, 0, n, k, partitions)) {
            return true;
        }
    }
    return false;
}

bool backtrack(const vector<int>& prefix, int start, int end, int target_k, vector<int>& partitions) {
    if (partitions.size() == target_k-1) {
        if (start == end) return false;
        
        // We have exactly target_k partitions now
        partitions.push_back(end);
        
        // Calculate sum of k-th partition
        int sum_k = prefix[end] - prefix[partitions[target_k-2]];
        // Calculate sum of (k-1)-th partition
        int sum_k_minus_1 = prefix[partitions[target_k-2]] - 
                           (target_k > 2 ? prefix[partitions[target_k-3]] : 0);
        
        partitions.pop_back();
        return sum_k % sum_k_minus_1 == 0;
    }
    
    for (int i = start+1; i <= end; i++) {
        partitions.push_back(i);
        if (backtrack(prefix, i, end, target_k, partitions)) {
            return true;
        }
        partitions.pop_back();
    }
    return false;
}

int main() {
    int n, m;
    cin >> n >> m;
    vector<int> nums(n);
    for (int i = 0; i < n; i++) {
        cin >> nums[i];
    }
    
    if (checkArrayPartition(nums, m)) {
        cout << "YES" << endl;
    } else {
        cout << "NO" << endl;
    }
    
    return 0;
}
