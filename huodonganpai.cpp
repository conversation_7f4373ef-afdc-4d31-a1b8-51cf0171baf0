#include <iostream>
#include <vector>
#include <algorithm>
using namespace std;

// 这个问题可以使用贪心算法来解决。首先，我们需要将所有活动按照结束时间从小到大进行排序，然后选择结束时间最早的活动。接下来，我们遍历其他活动，如果开始时间晚于上一个已选择的活动的结束时间，我们就选择该活动并更新上一个已选择活动的结束时间。最后，返回已选择的活动数即可。

struct Activity {
    int start;
    int end;
};

bool compare(Activity a, Activity b) {
    return a.end < b.end;
}

int maxActivities(vector<Activity>& activities) {
    int count = 1; // 至少有一个活动可以选择
    int prevEnd = activities[0].end;

    for (int i = 1; i < activities.size(); i++) {
        if (activities[i].start >= prevEnd) {
            count++;
            prevEnd = activities[i].end;
        }
    }

    return count;
}

int main() {
    int n;
    cin >> n;
    vector<Activity> activities(n);
    for (int i = 0; i < n; i++) {
        cin >> activities[i].start >> activities[i].end;
    }

    // sort(activities.begin(), activities.end(), compare);
    std::sort(activities.begin(), activities.end(), [](const Activity& a, const Activity& b) {
        return a.end < b.end;
    });

    int maxCount = maxActivities(activities);
    cout << maxCount << endl;

    return 0;
}

