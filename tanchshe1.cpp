def snake():
    # a1 = ['D', 'G', 'L', 'G', 'G', 'U', 'G', 'G', 'R', 'G', 'G', 'D', 'G', 'L', 'G']
    # n, m = 3, 3
    # a2 = [['F', 'F', 'F'], ['F', 'F', 'H'], ['E', 'F', 'E']]
    a1 = input().split(" ")
    n, m = [int(x) for x in input().split(" ")]
    a2 = []
    for i in range(n):
        a2.append(input().split(" "))
    # 找到起始位置
    start = (0, 0)
    for i in range(n):
        for j in range(m):
            if a2[i][j] == 'H':
                a2[i][j] = 'E'  # 头开始移动之后变为空格
                start = (i, j)
    body = [start]
    direction = 'L'
    for i in a1:
        if i == 'U':
            direction = 'U'
        elif i == 'D':
            direction = 'D'
        elif i == 'L':
            direction = 'L'
        elif i == 'R':
            direction = 'R'
        elif i == 'G':
            if direction == 'U':
                next = (body[0][0] - 1, body[0][1])
            elif direction == 'D':
                next = (body[0][0] + 1, body[0][1])
            elif direction == 'L':
                next = (body[0][0], body[0][1] - 1)
            else:
                next = (body[0][0], body[0][1] + 1)
            if next[0] < 0 or next[1] < 0 or next[0] > n - 1 or next[1] > m - 1 or next in body[:-1]:
                return len(body)
            if a2[next[0]][next[1]] == 'E':
                body = [next] + body[:-1]
            elif a2[next[0]][next[1]] == 'F':
                body = [next] + body[:]
                a2[next[0]][next[1]] = 'E'  # 吃完之后，变为空格
    return len(body)


print(snake())
