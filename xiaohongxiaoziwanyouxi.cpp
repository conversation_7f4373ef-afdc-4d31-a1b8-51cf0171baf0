#include <vector>
#include <iostream>
using namespace std;

int main() {
    int n;
    cin>>n;
    vector<int> nums1(n);
    vector<int> nums2(n);
    for(int i=0;i<n;i++)
        cin>>nums1[i];
    for(int i=0;i<n;i++)
        cin>>nums2[i];
    int res = 0;int cur = 0;
    for(int i=1;i<n;i++){
        if(nums1[i]-nums1[i-1]==nums2[i]-nums2[i-1])  cur++;
        else cur = 0;
        res = max(res,cur);
    }
    cout<<res+1<<endl;
    return 0;
}
