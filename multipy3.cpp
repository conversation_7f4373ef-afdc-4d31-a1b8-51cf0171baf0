#include <iostream>
#include <string>
#include <vector>
#include <algorithm>
using namespace std;

string findLargestMultipleOfThree(string digits) {
    // 统计每个数字的出现次数
    vector<int> count(10, 0);
    int sum = 0;
    
    for (char c : digits) {
        int digit = c - '0';
        count[digit]++;
        sum += digit;
    }
    
    // 如果所有数字都是0，特殊处理
    bool allZero = true;
    for (int i = 1; i <= 9; i++) {
        if (count[i] > 0) {
            allZero = false;
            break;
        }
    }
    if (allZero) {
        return count[0] > 0 ? "0" : "-1";
    }
    
    // 根据sum % 3的值来调整
    int remainder = sum % 3;
    
    if (remainder == 1) {
        // 需要移除余数为1的最小数字，或者移除两个余数为2的最小数字
        bool removed = false;
        
        // 尝试移除一个余数为1的最小数字
        for (int i = 1; i <= 9 && !removed; i += 3) {
            if (count[i] > 0) {
                count[i]--;
                removed = true;
            }
        }
        
        // 如果没有余数为1的数字，尝试移除两个余数为2的最小数字
        if (!removed) {
            int toRemove = 2;
            for (int i = 2; i <= 8 && toRemove > 0; i += 3) {
                int canRemove = min(count[i], toRemove);
                count[i] -= canRemove;
                toRemove -= canRemove;
            }
            if (toRemove > 0) {
                return "-1"; // 无法构成3的倍数
            }
        }
    } else if (remainder == 2) {
        // 需要移除余数为2的最小数字，或者移除两个余数为1的最小数字
        bool removed = false;
        
        // 尝试移除一个余数为2的最小数字
        for (int i = 2; i <= 8 && !removed; i += 3) {
            if (count[i] > 0) {
                count[i]--;
                removed = true;
            }
        }
        
        // 如果没有余数为2的数字，尝试移除两个余数为1的最小数字
        if (!removed) {
            int toRemove = 2;
            for (int i = 1; i <= 7 && toRemove > 0; i += 3) {
                int canRemove = min(count[i], toRemove);
                count[i] -= canRemove;
                toRemove -= canRemove;
            }
            if (toRemove > 0) {
                return "-1"; // 无法构成3的倍数
            }
        }
    }
    
    // 构建结果字符串（从大到小）
    string result = "";
    for (int i = 9; i >= 0; i--) {
        for (int j = 0; j < count[i]; j++) {
            result += (char)('0' + i);
        }
    }
    
    // 如果结果为空或者只有0
    if (result.empty()) {
        return "-1";
    }
    
    // 如果结果的第一个字符是0，说明所有非零数字都被移除了
    if (result[0] == '0') {
        return "0";
    }
    
    return result;
}

int main() {
    string digits;
    cin >> digits;
    cout << findLargestMultipleOfThree(digits) << endl;
    return 0;
}
