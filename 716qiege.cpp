#include <iostream>
#include <string>
#include <vector>
using namespace std;



void dfs(const std::string& str, std::vector<int> results, int pos, int current, int sum) {
    if (pos == str.length()) {
        sum += current;
        results.push_back(sum);
        return;
    }
    
    // 不插入加号，继续构建当前数字
    dfs(str, results, pos + 1, current * 10 + (str[pos] - '0'), sum);
    
    // 插入加号，将当前数字加到sum中，并重置current
    if (pos != 0) {  // 不在开头插入加号
        dfs(str, results, pos + 1, str[pos] - '0', sum + current);
    }
}



int main() {
    std::string str;
    cin >> str;

    std::vector<int> results;
    dfs(str, results, 0, 0, 0);

    long long sum = 0;
    for (int res : results) {
        sum += res;
    }
    cout <<sum << endl;

    return 0;
}