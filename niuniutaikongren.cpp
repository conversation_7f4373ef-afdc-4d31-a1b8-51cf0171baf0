// 【题解】牛牛太空人
// 题意
// 求从半径为R的球内部
// (
// 𝑥
// 0
// ,
// 𝑦
// 0
// ,
// 𝑧
// 0
// )
// (x 
// 0
// ​
//  ,y 
// 0
// ​
//  ,z 
// 0
// ​
//  )内质点以
// (
// 𝑣
// 𝑥
// ,
// 𝑣
// 𝑦
// ,
// 𝑣
// 𝑧
// )
// (v 
// x
// ​
//  ,v 
// y
// ​
//  ,v 
// z
// ​
//  )速度射出，求其碰到球面的时间

// 题解
// 可以直接二分时间，judge函数中求出在t时刻的坐标，判断坐标是在球内还是球外。若该坐标在球外说明时间太大应r=mid，反之说明时间太短了应l=mid。判断是在球内还是球外的方法，首先求出
// 𝑧
// z坐标，若
// 𝑧
// >
// 𝑅
// z>R则说明肯定在球外。否则求出此时坐标离z轴的距离
// 𝐿
// =
// 𝑥
// 2
// +
// 𝑦
// 2
// L= 
// x 
// 2
//  +y 
// 2
 
// ​
//  和该平面的圆的半径进行比较，圆半径即
// 𝑟
// =
// 𝑅
// 2
// −
// 𝑧
// 2
// r= 
// R 
// 2
//  −z 
// 2
 
// ​
//  ，若
// 𝐿
// <
// 𝑟
// L<r则说明还在球内部，反之在球外。
// ##复杂度
// 时间复杂度为
// 𝑂
// (
// 𝑙
// 𝑜
// 𝑔
// 𝑛
// )
// O(logn)
// 空间复杂度为
// 𝑂
// (
// 1
// )
// O(1)

#include<bits/stdc++.h>
using namespace std;
const double eps=1e-10;
double x_,y_,z_,vx,vy,vz,R;
int judge(double t)
{
    double x,y,z;
    x=x_+t*vx;
    y=y_+t*vy;
    z=z_+t*vz;
    if(fabs(z)>fabs(R))
        return 1; // 超过边界。
    else
    {
        double r=sqrt(R*R-z*z); // 垂直于Z轴的平面。
        double l=sqrt(x*x+y*y);
        if(l>r)
            return 1;
        else
            return 0;
    }
}
int main()
{
    int t;
    scanf("%d",&t);
    while(t--)
    {
        scanf("%lf%lf%lf%lf%lf%lf%lf",&x_,&y_,&z_,&vx,&vy,&vz,&R);
        double l=0;
        double r=9999999999;
        while(fabs(l-r)>eps)
        {
            double mid=(l+r)/2;
            if(judge(mid))
                r=mid;
            else
                l=mid;
        }
        printf("%.10f\n",l);
    }
    return 0;
}
