#include <iostream>
#include <vector>
#include <algorithm>
using namespace std;

class UnionFind {
private:
    vector<int> parent;
    vector<int> rank;
    vector<int> size;
public:
    UnionFind(int n) {
        parent.resize(n);
        rank.resize(n, 0);
        size.resize(n, 1);
        for (int i = 0; i < n; i++) {
            parent[i] = i;
        }
    }

    int find(int x) {
        if (parent[x] != x) {
            parent[x] = find(parent[x]); // Path compression
        }
        return parent[x];
    }

    void unite(int x, int y) {
        int rootX = find(x);
        int rootY = find(y);
        if (rootX != rootY) {
            // Union by rank
            if (rank[rootX] > rank[rootY]) {
                parent[rootY] = rootX;
                size[rootX] += size[rootY];
            } else {
                parent[rootX] = rootY;
                size[rootY] += size[rootX];
                if (rank[rootX] == rank[rootY]) {
                    rank[rootY]++;
                }
            }
        }
    }

    int getSize(int x) {
        return size[find(x)];
    }
};

int maxAreaOfIsland(vector<vector<int>>& grid) {
    if (grid.empty() || grid[0].empty()) return 0;
    
    int m = grid.size();
    int n = grid[0].size();
    UnionFind uf(m * n);
    bool hasIsland = false;
    
    // Initialize Union-Find with all land cells
    for (int i = 0; i < m; i++) {
        for (int j = 0; j < n; j++) {
            if (grid[i][j] == 1) {
                hasIsland = true;
                int idx = i * n + j;
                // Check left neighbor
                if (j > 0 && grid[i][j-1] == 1) {
                    uf.unite(idx, i * n + (j - 1));
                }
                // Check top neighbor
                if (i > 0 && grid[i-1][j] == 1) {
                    uf.unite(idx, (i - 1) * n + j);
                }
            }
        }
    }
    
    if (!hasIsland) return 0;
    
    // Find maximum size
    int maxArea = 0;
    for (int i = 0; i < m; i++) {
        for (int j = 0; j < n; j++) {
            if (grid[i][j] == 1) {
                maxArea = max(maxArea, uf.getSize(i * n + j));
            }
        }
    }
    return maxArea;
}

int main() {
    vector<vector<int>> grid1 = {
        {1,1,0,0,0},
        {1,1,0,0,0},
        {0,0,0,1,1},
        {0,0,0,1,1}
    };
    cout << "Test case 1: " << maxAreaOfIsland(grid1) << endl;

    vector<vector<int>> grid2 = {
        {0,0,1,0,0},
        {0,1,1,1,0},
        {0,1,0,1,0},
        {0,1,1,1,0},
        {0,0,0,0,0}
    };
    cout << "Test case 2: " << maxAreaOfIsland(grid2) << endl;

    return 0;
}
