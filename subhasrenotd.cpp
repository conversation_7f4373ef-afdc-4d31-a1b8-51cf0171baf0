
#include <iostream>
#include <vector>
#include <string>
using namespace std;

int countSubstrings(const string& s) {
    int n = s.size();
    int res = 0;
    vector<bool> has_r(n + 1, false), has_e(n + 1, false);

    // 预处理has_r和has_e数组, 0 到 i之前是否有kk.
    for (int i = 1; i <= n; ++i) {
        if (s[i - 1] == 'r') has_r[i] = true;
        if (s[i - 1] == 'e') has_e[i] = true;
        if (i > 1) {
            has_r[i] = has_r[i] | has_r[i - 1];
            has_e[i] = has_e[i] | has_e[i - 1];
        }
    }

    // 暴力枚举所有子串
    for (int i = 0; i < n; ++i) {
        for (int j = i + 1; j <= n; ++j) {
            bool no_d = true;
            for (int k = i; k < j; ++k) {
                if (s[k] == 'd') {
                    no_d = false;
                    break;
                }
            }
            if (no_d && has_r[j] && has_e[j] && !(has_r[i] && has_e[i])) {
                res++;
            }
        }
    }
    return res;
}

int main() {
    string s;
    cin >> s;
    cout << countSubstrings(s) << endl;
    return 0;
}
