
#include <iostream>
#include <string>
using namespace std;

int countSubstrings(const string& s) {
    int n = s.size();
    int count = 0;

    // 枚举所有可能的子串
    for (int i = 0; i < n; i++) {
        bool has_r = false, has_e = false, has_d = false;

        for (int j = i; j < n; j++) {
            // 更新当前子串s[i...j]的字符状态
            if (s[j] == 'r') has_r = true;
            if (s[j] == 'e') has_e = true;
            if (s[j] == 'd') has_d = true;

            // 如果遇到'd'，后续的子串都会包含'd'，可以跳出
            if (has_d) break;

            // 检查是否同时包含'r'和'e'且不包含'd'
            if (has_r && has_e) {
                count++;
            }
        }
    }

    return count;
}

int main() {
    string s;
    cin >> s;
    cout << countSubstrings(s) << endl;
    return 0;
}
