#include <iostream>
#include <vector>
#include <string>
#include <climits>
#include <algorithm>
using namespace std;

int minChangesToFix(string s) {
    int n = s.size();
    // dp[i][j] 表示将子串 s[i...j] 变成合法括号序列的最小修改次数
    vector<vector<int>> dp(n, vector<int>(n, 0));
    
    // 枚举所有可能的子串长度（必须是偶数）
    for (int len = 2; len <= n; len += 2) {
        for (int i = 0; i + len - 1 < n; i++) {
            int j = i + len - 1;
            dp[i][j] = INT_MAX;
            
            // 情况1: 将 s[i] 和 s[j] 作为一对括号
            int matchCost = 0;
            
            // 检查 s[i] 和 s[j] 是否可以匹配
            if ((s[i] == '{' && s[j] == '}') || (s[i] == '[' && s[j] == ']')) {
                // 已经匹配，不需要修改
                matchCost = 0;
            } else if ((s[i] == '{' || s[i] == '[') && (s[j] == '}' || s[j] == ']')) {
                // 一个是左括号，一个是右括号，但类型不匹配，需要修改一个
                matchCost = 1;
            } else {
                // 两个都是左括号或都是右括号，需要修改两个
                matchCost = 2;
            }
            
            // 如果将 s[i] 和 s[j] 匹配，中间部分的最小修改次数
            int innerCost = (i + 1 <= j - 1) ? dp[i + 1][j - 1] : 0;
            dp[i][j] = min(dp[i][j], matchCost + innerCost);
            
            // 情况2: 将区间 [i, j] 分割成两个独立的合法括号序列
            for (int k = i + 1; k < j; k += 2) {
                dp[i][j] = min(dp[i][j], dp[i][k] + dp[k + 1][j]);
            }
        }
    }
    
    return dp[0][n - 1];
}

int main() {
    int t;
    cin >> t;
    
    while (t--) {
        string s;
        cin >> s;
        cout << minChangesToFix(s) << endl;
    }
    
    return 0;
}
