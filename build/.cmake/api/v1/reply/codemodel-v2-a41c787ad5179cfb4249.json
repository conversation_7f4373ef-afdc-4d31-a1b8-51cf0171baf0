{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "HelloWorld", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "HelloWorld::@6890427a1f51a3e7e1df", "jsonFile": "target-HelloWorld-df970a9d3acebc217eb0.json", "name": "HelloWorld", "projectIndex": 0}, {"directoryIndex": 0, "id": "plus::@6890427a1f51a3e7e1df", "jsonFile": "target-plus-8aaa958efc058dad9f60.json", "name": "plus", "projectIndex": 0}, {"directoryIndex": 0, "id": "tanchishe::@6890427a1f51a3e7e1df", "jsonFile": "target-tanchishe-150418c75142f095f456.json", "name": "tanchi<PERSON>", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/github/niucoder/build", "source": "/home/<USER>/github/niucoder"}, "version": {"major": 2, "minor": 6}}