# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/github/niucoder

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/github/niucoder/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/HelloWorld.dir/all
all: CMakeFiles/tanchishe.dir/all
all: CMakeFiles/plus.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/HelloWorld.dir/clean
clean: CMakeFiles/tanchishe.dir/clean
clean: CMakeFiles/plus.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/HelloWorld.dir

# All Build rule for target.
CMakeFiles/HelloWorld.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/HelloWorld.dir/build.make CMakeFiles/HelloWorld.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/HelloWorld.dir/build.make CMakeFiles/HelloWorld.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/github/niucoder/build/CMakeFiles --progress-num=1,2 "Built target HelloWorld"
.PHONY : CMakeFiles/HelloWorld.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/HelloWorld.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/github/niucoder/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/HelloWorld.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/github/niucoder/build/CMakeFiles 0
.PHONY : CMakeFiles/HelloWorld.dir/rule

# Convenience name for target.
HelloWorld: CMakeFiles/HelloWorld.dir/rule
.PHONY : HelloWorld

# clean rule for target.
CMakeFiles/HelloWorld.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/HelloWorld.dir/build.make CMakeFiles/HelloWorld.dir/clean
.PHONY : CMakeFiles/HelloWorld.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/tanchishe.dir

# All Build rule for target.
CMakeFiles/tanchishe.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tanchishe.dir/build.make CMakeFiles/tanchishe.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tanchishe.dir/build.make CMakeFiles/tanchishe.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/github/niucoder/build/CMakeFiles --progress-num=5,6 "Built target tanchishe"
.PHONY : CMakeFiles/tanchishe.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/tanchishe.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/github/niucoder/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/tanchishe.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/github/niucoder/build/CMakeFiles 0
.PHONY : CMakeFiles/tanchishe.dir/rule

# Convenience name for target.
tanchishe: CMakeFiles/tanchishe.dir/rule
.PHONY : tanchishe

# clean rule for target.
CMakeFiles/tanchishe.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tanchishe.dir/build.make CMakeFiles/tanchishe.dir/clean
.PHONY : CMakeFiles/tanchishe.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/plus.dir

# All Build rule for target.
CMakeFiles/plus.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/plus.dir/build.make CMakeFiles/plus.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/plus.dir/build.make CMakeFiles/plus.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/github/niucoder/build/CMakeFiles --progress-num=3,4 "Built target plus"
.PHONY : CMakeFiles/plus.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/plus.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/github/niucoder/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/plus.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/github/niucoder/build/CMakeFiles 0
.PHONY : CMakeFiles/plus.dir/rule

# Convenience name for target.
plus: CMakeFiles/plus.dir/rule
.PHONY : plus

# clean rule for target.
CMakeFiles/plus.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/plus.dir/build.make CMakeFiles/plus.dir/clean
.PHONY : CMakeFiles/plus.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

